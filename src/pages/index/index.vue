<template>
  <view class="content">
    <view class="title-section">
      <text class="title">AI 互动故事</text>
      <text class="subtitle">请选择你的性别</text>
    </view>

    <view class="gender-selection">
      <button class="gender-button" @click="selectGender('male')">男性</button>
      <button class="gender-button" @click="selectGender('female')">女性</button>
    </view>

    <scroll-view v-if="gender" class="story-grid" scroll-y="true">
      <text class="story-title">请选择你感兴趣的故事类型</text>
      <view class="grid">
        <view class="grid-item" v-for="(story, index) in storyTypes" :key="index" @click="startGame(story)">
          <text class="grid-text">{{ story.title }}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const gender = ref<'male' | 'female' | null>(null)

const maleStories = [
  { id: 'sci-fi', title: '星际远航' },
  { id: 'mystery', title: '都市迷案' },
  { id: 'fantasy', title: '魔幻史诗' },
  { id: 'wuxia', title: '武林争霸' },
  { id: 'adventure', title: '荒岛求生' },
  { id: 'horror', title: '古宅魅影' },
  { id: 'romance', title: '浪漫邂逅' },
  { id: 'history', title: '历史回响' },
]

const femaleStories = [
  { id: 'romance', title: '宫廷绝恋' },
  { id: 'fantasy', title: '仙境奇缘' },
  { id: 'mystery', title: '谜案追踪' },
  { id: 'slice-of-life', title: '都市生活' },
  { id: 'adventure', title: '秘境探险' },
  { id: 'history', title: '古代仕女' },
  { id: 'sci-fi', title: '未来之城' },
  { id: 'horror', title: '校园怪谈' },
]

const storyTypes = computed(() => {
  return gender.value === 'male' ? maleStories : femaleStories
})

const selectGender = (selectedGender: 'male' | 'female') => {
  gender.value = selectedGender
}

const startGame = (story: { id: string; title: string }) => {
  uni.navigateTo({
    url: `/pages/story/story?gender=${gender.value}&storyType=${story.id}&storyTitle=${story.title}`
  })
}
</script>

<style>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: #f0f2f5;
  height: 100vh;
}

.title-section {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
}

.subtitle {
  font-size: 18px;
  color: #666;
  margin-top: 10px;
}

.gender-selection {
  display: flex;
  margin-bottom: 30px;
}

.gender-button {
  margin: 0 15px;
  padding: 10px 30px;
  font-size: 16px;
  border-radius: 25px;
  background-color: #007aff;
  color: white;
}

.story-grid {
  width: 100%;
  height: 400px; /* 确保有足够的高度来滚动 */
}

.story-title {
  font-size: 18px;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  width: 100%;
}

.grid-item {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.grid-item:active {
    transform: scale(0.95);
}

.grid-text {
  font-size: 16px;
  color: #333;
}
</style>
