<template>
  <view class="story-container">
    <view class="loading-view" v-if="loading">
      <text class="loading-text">正在努力生成中...</text>
    </view>

    <scroll-view class="content-scroll" scroll-y="true">
      <image class="story-image" :src="storyImage" mode="aspectFill" v-if="storyImage"></image>
      <view class="story-text-panel">
        <text class="story-text">{{ currentStory }}</text>
      </view>
    </scroll-view>

    <view class="options-panel">
      <button
        class="option-button"
        v-for="(option, index) in options"
        :key="index"
        @click="makeChoice(index)"
        :disabled="loading"
      >
        {{ option }}
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getInitialStory, getNextStory } from '../../utils/api'

const props = defineProps({
  gender: String,
  storyType: String,
  storyTitle: String
})

const loading = ref(true)
const storyImage = ref('')
const currentStory = ref('')
const storyHistory = ref<string[]>([])
const options = ref<string[]>([])

onMounted(() => {
  startNewGame()
})

const startNewGame = async () => {
  loading.value = true
  try {
    const initialPrompt = `我是一个${props.gender === 'male' ? '男性' : '女性'}，我想体验一个关于“${props.storyTitle}”的故事。请为我生成故事的开篇，并给我三个选项。`
    const result = await getInitialStory(initialPrompt)

    storyImage.value = result.imageUrl
    currentStory.value = result.story
    options.value = result.options
    storyHistory.value.push(result.story)

  } catch (error) {
    uni.showToast({ title: '故事生成失败，请重试', icon: 'none' })
    console.error(error)
  } finally {
    loading.value = false
  }
}

const makeChoice = async (choiceIndex: number) => {
  loading.value = true
  const choice = options.value[choiceIndex]
  storyHistory.value.push(`我选择了：${choice}`)

  try {
    const result = await getNextStory(storyHistory.value)

    storyImage.value = result.imageUrl
    currentStory.value = result.story
    options.value = result.options
    storyHistory.value.push(result.story)

  } catch (error) {
    uni.showToast({ title: '故事发展失败，请重试', icon: 'none' })
    console.error(error)
  } finally {
    loading.value = false
  }
}
</script>

<style>
.story-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #333;
  color: white;
}

.loading-view {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0,0,0,0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 99;
}

.loading-text {
    font-size: 18px;
    color: #fff;
}

.content-scroll {
  flex: 1;
}

.story-image {
  width: 100%;
  height: 250px; /* or adjust as needed */
}

.story-text-panel {
  padding: 20px;
}

.story-text {
  font-size: 16px;
  line-height: 1.6;
}

.options-panel {
  padding: 20px;
  border-top: 1px solid #444;
}

.option-button {
  background-color: #555;
  color: white;
  margin-top: 10px;
  border-radius: 20px;
}

.option-button:disabled {
    background-color: #222;
    color: #666;
}

</style>
