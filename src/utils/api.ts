import * as CryptoJS from 'crypto-js';

// --- 安全警告 ---
// 请务必将您的真实密钥存储在安全的地方，例如通过环境变量注入，
// 而不是直接硬编码在代码中，以防泄露。
const API_KEY = 'YOUR_API_KEY';       // 替换为您自己的 API Key
const SECRET_KEY = 'YOUR_SECRET_KEY';   // 替换为您自己的 Secret Key

const HOST = 'maas-api.ml-platform-cn-beijing.volces.com';
const REGION = 'cn-beijing';
const SERVICE = 'ml_maas';

/**
 * 构建符合火山引擎要求的签名认证
 * @param method HTTP请求方法 (e.g., 'POST')
 * @param path 请求路径 (e.g., '/api/v1/chat')
 * @param query 查询参数
 * @param body 请求体 (JSON string)
 * @returns 返回包含认证头信息的对象
 */
function getAuthorization(method: string, path: string, query: any, body: string) {
    const isoDate = new Date().toISOString();
    const shortDate = isoDate.substr(0, 10).replace(/-/g, '');

    // 1. 创建 Canonical Request
    const canonicalHeaders = `host:${HOST}\nx-date:${isoDate}\n`;
    const signedHeaders = 'host;x-date';
    const hashedPayload = CryptoJS.SHA256(body).toString(CryptoJS.enc.Hex);
    const canonicalRequest = `${method}\n${path}\n${queryToString(query)}\n${canonicalHeaders}\n${signedHeaders}\n${hashedPayload}`;

    // 2. 创建 String to Sign
    const credentialScope = `${shortDate}/${REGION}/${SERVICE}/request`;
    const hashedCanonicalRequest = CryptoJS.SHA256(canonicalRequest).toString(CryptoJS.enc.Hex);
    const stringToSign = `HMAC-SHA256\n${isoDate}\n${credentialScope}\n${hashedCanonicalRequest}`;

    // 3. 计算签名 (Signing Key)
    const kDate = CryptoJS.HmacSHA256(shortDate, SECRET_KEY);
    const kRegion = CryptoJS.HmacSHA256(REGION, kDate);
    const kService = CryptoJS.HmacSHA256(SERVICE, kRegion);
    const kSigning = CryptoJS.HmacSHA256('request', kService);
    const signature = CryptoJS.HmacSHA256(stringToSign, kSigning).toString(CryptoJS.enc.Hex);

    // 4. 构建 Authorization Header
    const authorization = `HMAC-SHA256 Credential=${API_KEY}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;

    return {
        'Authorization': authorization,
        'X-Date': isoDate,
        'Content-Type': 'application/json'
    };
}

function queryToString(query: any) {
    return Object.keys(query).sort().map(key => `${key}=${query[key]}`).join('&');
}

interface StoryResult {
    imageUrl: string;
    story: string;
    options: string[];
}

/**
 * 调用豆包文生文大模型
 * @param messages 对话历史
 * @returns AI生成的回复
 */
async function generateText(messages: any[]) {
    const path = '/api/v1/chat';
    const endpoint = `https://${HOST}${path}`;
    const body = JSON.stringify({
        model: {
            name: 'Doubao-pro-32k', // 或者选择其他适合的模型
        },
        messages,
        // 要求AI返回JSON格式，包含故事和选项
        tools: [{
            type: "json_object",
            json_object: {
                name: "story_and_options",
                description: "根据故事生成下一步情节和三个选项",
                properties: {
                    story: { type: "string", description: "生成的故事情节" },
                    options: { type: "array", items: { type: "string" }, description: "给用户的三个选项" }
                },
                required: ["story", "options"]
            }
        }]
    });

    const headers = getAuthorization('POST', path, {}, body);

    const response = await uni.request({
        url: endpoint,
        method: 'POST',
        header: headers,
        data: body
    });

    if (response.statusCode !== 200 || !response.data.choices) {
        console.error('文生文API失败:', response);
        throw new Error('AI a story.');
    }

    // 解析AI返回的JSON字符串
    const resultJson = JSON.parse(response.data.choices[0].message.content);
    return resultJson;
}

/**
 * 调用豆包文生图模型
 * @param prompt 图片描述
 * @returns 图片URL
 */
async function generateImage(prompt: string): Promise<string> {
    // 注意：文生图API的路径和请求体结构请以最新官方文档为准
    // 这里提供一个示例结构
    const path = '/api/v1/image-generate'; // 请根据文档确认真实路径
    const endpoint = `https://${HOST}${path}`;
    const body = JSON.stringify({
        model: {
            name: 'Doubao-image-model', // 请根据文档确认真实模型名称
        },
        prompt: `动漫风格, ${prompt}` // 添加风格提示词
    });

    const headers = getAuthorization('POST', path, {}, body);

    const response = await uni.request({
        url: endpoint,
        method: 'POST',
        header: headers,
        data: body
    });

    if (response.statusCode !== 200 || !response.data.imageUrl) {
        console.error('文生图API失败:', response);
        // 如果图片生成失败，返回一个占位图，不中断故事
        return 'https://picsum.photos/seed/error/400/250';
    }

    return response.data.imageUrl;
}


export const getInitialStory = async (initialPrompt: string): Promise<StoryResult> => {
    const messages = [{ role: 'user', content: initialPrompt }];
    const storyData = await generateText(messages);
    const imagePrompt = storyData.story.substring(0, 50); // 用故事开头做图片prompt
    const imageUrl = await generateImage(imagePrompt);

    return {
        ...storyData,
        imageUrl
    };
};

export const getNextStory = async (history: string[]): Promise<StoryResult> => {
    const messages = history.map((item, index) => ({
        role: index % 2 === 0 ? 'user' : 'assistant',
        content: item
    }));
    
    const storyData = await generateText(messages);
    const imagePrompt = storyData.story.substring(0, 50);
    const imageUrl = await generateImage(imagePrompt);

    return {
        ...storyData,
        imageUrl
    };
};